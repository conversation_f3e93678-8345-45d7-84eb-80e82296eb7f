/* Search Bar Component CSS */

/* CSS Custom Properties */
:root {
    /* Primary colors */
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #000000;
    --accent-hover: #333333;
    --background-primary: #ffffff;
    --background-secondary: #ffffff;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-hint: #999999;
    --border-color: #cccccc;
    --shadow-light: rgba(0, 0, 0, 0.12);
    --shadow-medium: rgba(0, 0, 0, 0.16);
    --shadow-dark: rgba(0, 0, 0, 0.24);
    --input-background: #ffffff;
    --input-border: #cccccc;
    --input-focus: #000000;

    /* Search specific colors */
    --search-item-hover-background: #f0f0f0;
    --search-item-hover-text: #000000;
    --search-item-selected-background: #000000;
    --search-item-selected-text: #ffffff;

    /* Header colors */
    --header-background: #000000;
    --header-text: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

body {
    background: var(--background-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Search Bar Component Container */
.search-bar-component {
    position: relative;
    width: 100%;
}

/* Header Styles */
.search-header {
    background: var(--header-background);
    color: var(--header-text);
    position: sticky;
    top: 0;
    z-index: 10000;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-light);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem 2rem;
    height: 70px;
    gap: 2rem;
}

.header-left {
    flex-shrink: 0;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.brand-name {
    color: var(--header-text);
}

.header-center {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
}

.header-right {
    flex-shrink: 0;
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--input-background);
    border: 2px solid var(--input-border);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 600px;
}

.search-container:focus-within {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.search-icon {
    color: var(--text-hint);
    margin-right: 0.75rem;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.search-input {
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 1rem;
    flex: 1;
    outline: none;
    min-width: 0;
}

.search-input::placeholder {
    color: var(--text-hint);
}

.search-shortcut {
    margin-left: 0.75rem;
    flex-shrink: 0;
}

.search-shortcut span {
    background: var(--text-hint);
    color: var(--input-background);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.search-settings-btn {
    background: transparent;
    border: none;
    color: var(--header-text);
    padding: 0.75rem;
    font-size: 1.1rem;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.search-settings-btn:hover {
    background: var(--accent-hover);
}

/* Search Results Dropdown */
.search-results {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 700px;
    background: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 12px 48px var(--shadow-dark);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s ease;
    z-index: 9999;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.search-results.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.search-results-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-secondary);
    flex-shrink: 0;
}

.search-results-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-count {
    font-weight: 600;
    color: var(--text-primary);
}

.search-term {
    color: var(--text-secondary);
    font-style: italic;
}

.search-results-close {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.search-results-close:hover {
    background: var(--search-item-hover-background);
    color: var(--text-primary);
}

.search-results-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

/* Search Sections */
.search-section {
    margin-bottom: 1.5rem;
}

.search-section:last-child {
    margin-bottom: 0;
}

.search-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0.75rem;
}

.search-section-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-recent-btn {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.clear-recent-btn:hover {
    background: var(--search-item-hover-background);
    color: var(--text-primary);
}

/* Search Items */
.search-items {
    display: flex;
    flex-direction: column;
}

.search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.search-item:hover,
.search-item.selected {
    background: var(--search-item-hover-background);
    color: var(--search-item-hover-text);
}

.search-item.selected {
    background: var(--search-item-selected-background);
    color: var(--search-item-selected-text);
}

.search-item-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--accent-color);
    color: var(--header-text);
    border-radius: 8px;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.search-item-content {
    flex: 1;
    min-width: 0;
}

.search-item-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-item-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    flex-shrink: 0;
}

.search-item-category {
    font-size: 0.75rem;
    color: var(--text-hint);
    background: var(--background-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* No Results */
.search-no-results {
    padding: 3rem 1.5rem;
    text-align: center;
}

.no-results-content i {
    font-size: 3rem;
    color: var(--text-hint);
    margin-bottom: 1rem;
}

.no-results-content h4 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.no-results-content p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.search-suggestions h5 {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.suggestion-tag {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-tag:hover {
    background: var(--accent-color);
    color: var(--header-text);
    border-color: var(--accent-color);
}

/* Search Results Footer */
.search-results-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--background-secondary);
    flex-shrink: 0;
}

.search-navigation-hint {
    display: flex;
    gap: 1rem;
    justify-content: center;
    font-size: 0.8rem;
    color: var(--text-hint);
}

.search-navigation-hint kbd {
    background: var(--text-hint);
    color: var(--input-background);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 9998;
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        gap: 1rem;
    }
    
    .header-left,
    .header-right {
        display: none;
    }
    
    .header-center {
        max-width: 100%;
    }
    
    .search-results {
        width: 95%;
        max-height: 70vh;
    }
    
    .search-navigation-hint {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
