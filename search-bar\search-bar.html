<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Bar Component</title>
    <link rel="stylesheet" href="search-bar.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Search Bar Component -->
    <div class="search-bar-component">
        <!-- Header with Search Bar -->
        <header class="search-header">
            <div class="header-content">
                <!-- Left: Brand Name (Optional) -->
                <div class="header-left">
                    <h1 class="header-title">
                        <span class="brand-name">Your App</span>
                    </h1>
                </div>

                <!-- Center: Search Bar -->
                <div class="header-center">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="search-input" class="search-input" 
                               placeholder="Search all menus, items, and bookmarks... (Ctrl+K)" />
                        <div class="search-shortcut">
                            <span>Ctrl+K</span>
                        </div>
                    </div>
                </div>

                <!-- Right: Additional Controls (Optional) -->
                <div class="header-right">
                    <button class="search-settings-btn" title="Search Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Search Results Dropdown -->
        <div class="search-results" id="search-results">
            <div class="search-results-header">
                <div class="search-results-info">
                    <span class="results-count">0 results</span>
                    <span class="search-term"></span>
                </div>
                <button class="search-results-close" id="search-results-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="search-results-content">
                <!-- Recent Searches -->
                <div class="search-section" id="recent-searches-section">
                    <div class="search-section-header">
                        <h4>Recent Searches</h4>
                        <button class="clear-recent-btn" id="clear-recent-searches">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                    <div class="search-items" id="recent-searches">
                        <!-- Recent searches will be populated here -->
                    </div>
                </div>

                <!-- Search Results -->
                <div class="search-section" id="search-results-section">
                    <div class="search-section-header">
                        <h4>Search Results</h4>
                    </div>
                    <div class="search-items" id="search-items">
                        <!-- Search results will be populated here -->
                    </div>
                </div>

                <!-- No Results -->
                <div class="search-no-results" id="search-no-results" style="display: none;">
                    <div class="no-results-content">
                        <i class="fas fa-search"></i>
                        <h4>No results found</h4>
                        <p>Try adjusting your search terms or browse our categories.</p>
                        <div class="search-suggestions">
                            <h5>Popular searches:</h5>
                            <div class="suggestion-tags">
                                <button class="suggestion-tag" data-suggestion="dashboard">Dashboard</button>
                                <button class="suggestion-tag" data-suggestion="reports">Reports</button>
                                <button class="suggestion-tag" data-suggestion="settings">Settings</button>
                                <button class="suggestion-tag" data-suggestion="users">Users</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results Footer -->
            <div class="search-results-footer">
                <div class="search-navigation-hint">
                    <span><kbd>↑</kbd><kbd>↓</kbd> to navigate</span>
                    <span><kbd>Enter</kbd> to select</span>
                    <span><kbd>Esc</kbd> to close</span>
                </div>
            </div>
        </div>

        <!-- Search Overlay -->
        <div class="search-overlay" id="search-overlay"></div>
    </div>

    <!-- Sample Data for Demo -->
    <script>
        // Sample search data - replace with your actual data source
        window.searchData = [
            {
                id: 'dashboard-main',
                name: 'Main Dashboard',
                type: 'page',
                icon: 'fas fa-tachometer-alt',
                href: '/dashboard',
                category: 'Dashboard',
                description: 'Main dashboard overview'
            },
            {
                id: 'users-management',
                name: 'User Management',
                type: 'page',
                icon: 'fas fa-users',
                href: '/users',
                category: 'Administration',
                description: 'Manage system users'
            },
            {
                id: 'reports-analytics',
                name: 'Analytics Reports',
                type: 'page',
                icon: 'fas fa-chart-bar',
                href: '/reports/analytics',
                category: 'Reports',
                description: 'View analytics and reports'
            },
            {
                id: 'settings-general',
                name: 'General Settings',
                type: 'page',
                icon: 'fas fa-cog',
                href: '/settings',
                category: 'Settings',
                description: 'Configure general settings'
            },
            {
                id: 'helpdesk-tickets',
                name: 'Support Tickets',
                type: 'feature',
                icon: 'fas fa-ticket-alt',
                href: '/helpdesk/tickets',
                category: 'Helpdesk',
                description: 'Manage support tickets'
            },
            {
                id: 'inventory-parts',
                name: 'Parts Inventory',
                type: 'feature',
                icon: 'fas fa-boxes',
                href: '/inventory/parts',
                category: 'Inventory',
                description: 'Manage parts inventory'
            }
        ];
    </script>

    <script src="search-bar.js"></script>
</body>
</html>
